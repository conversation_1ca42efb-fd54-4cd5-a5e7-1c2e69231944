import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';

import { AuthGuard } from '@/components/AuthGuard';
import { useColorScheme } from '@/hooks/useColorScheme';
import { TRPCProvider } from '@/lib/providers/TRPCProvider';
import { useAuthStore } from '@/lib/stores/authStore';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const loadAuthState = useAuthStore((state) => state.loadAuthState);
  const initializeNotifications = useNotificationsStore((state) => state.initializeNotifications);

  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    loadAuthState();
    initializeNotifications();
  }, [loadAuthState, initializeNotifications]);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <TRPCProvider>
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <AuthGuard>
          <Stack>
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen name="auth/login" options={{ headerShown: false }} />
            <Stack.Screen name="+not-found" />
          </Stack>
        </AuthGuard>
        <StatusBar style="auto" />
      </ThemeProvider>
    </TRPCProvider>
  );
}
