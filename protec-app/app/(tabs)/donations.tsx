import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { <PERSON><PERSON>, <PERSON>, LoadingSpinner, Modal } from '@/components/ui';
import { useAuthStore } from '@/lib/stores/authStore';
import { Donation, DonationFrequency } from '@/lib/types';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    FlatList,
    RefreshControl,
    ScrollView,
    TouchableOpacity,
    View
} from 'react-native';

interface DonationCampaign {
  id: string;
  title: string;
  description: string;
  goal: number;
  raised: number;
  endDate: Date;
  imageUrl?: string;
  isActive: boolean;
}

export default function DonationsScreen() {
  const [activeTab, setActiveTab] = useState<'campaigns' | 'history' | 'impact'>('campaigns');
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showQuickDonateModal, setShowQuickDonateModal] = useState(false);
  
  const { user, isAuthenticated } = useAuthStore();

  // Mock data - replace with actual API calls
  const [campaigns] = useState<DonationCampaign[]>([
    {
      id: '1',
      title: 'Student Scholarship Fund',
      description: 'Help provide scholarships for deserving students who need financial assistance to pursue their education at PROTEC.',
      goal: 500000,
      raised: 325000,
      endDate: new Date('2024-12-31'),
      imageUrl: undefined,
      isActive: true,
    },
    {
      id: '2',
      title: 'Infrastructure Development',
      description: 'Support the development of new laboratories and learning facilities to enhance the educational experience.',
      goal: 1000000,
      raised: 450000,
      endDate: new Date('2024-06-30'),
      imageUrl: undefined,
      isActive: true,
    },
  ]);

  const [donationHistory] = useState<Donation[]>([
    {
      id: '1',
      donor: user!,
      donorId: user?.id || '',
      amount: 1000,
      currency: 'ZAR',
      frequency: DonationFrequency.ONE_TIME,
      campaign: 'Student Scholarship Fund',
      isAnonymous: false,
      paymentMethod: 'PayFast',
      paymentReference: 'PF123456789',
      status: 'completed',
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
    },
  ]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // TODO: Implement API call to refresh data
      await new Promise(resolve => setTimeout(resolve, 1000));
    } finally {
      setRefreshing(false);
    }
  };

  const handleDonate = (campaignId?: string) => {
    if (!isAuthenticated) {
      Alert.alert(
        'Login Required',
        'Please log in to make a donation.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Login', onPress: () => router.push('/auth/login') },
        ]
      );
      return;
    }
    
    router.push(`/donations/create${campaignId ? `?campaign=${campaignId}` : ''}`);
  };

  const handleQuickDonate = () => {
    setShowQuickDonateModal(true);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
    }).format(amount);
  };

  const calculateProgress = (raised: number, goal: number) => {
    return Math.min((raised / goal) * 100, 100);
  };

  const renderCampaignItem = ({ item }: { item: DonationCampaign }) => (
    <Card variant="elevated" style={styles.campaignCard}>
      <View style={styles.campaignHeader}>
        <ThemedText type="subtitle" style={styles.campaignTitle}>
          {item.title}
        </ThemedText>
        <View style={styles.campaignStatus}>
          <ThemedText style={styles.statusText}>
            {item.isActive ? 'Active' : 'Ended'}
          </ThemedText>
        </View>
      </View>
      
      <ThemedText style={styles.campaignDescription}>
        {item.description}
      </ThemedText>
      
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { width: `${calculateProgress(item.raised, item.goal)}%` }
            ]} 
          />
        </View>
        <View style={styles.progressStats}>
          <ThemedText style={styles.progressText}>
            {formatCurrency(item.raised)} raised of {formatCurrency(item.goal)} goal
          </ThemedText>
          <ThemedText style={styles.progressPercentage}>
            {calculateProgress(item.raised, item.goal).toFixed(1)}%
          </ThemedText>
        </View>
      </View>
      
      <View style={styles.campaignActions}>
        <Button
          title="Donate Now"
          onPress={() => handleDonate(item.id)}
          style={styles.donateButton}
        />
        <Button
          title="Learn More"
          onPress={() => router.push(`/donations/campaigns/${item.id}`)}
          variant="outline"
          style={styles.learnMoreButton}
        />
      </View>
    </Card>
  );

  const renderDonationItem = ({ item }: { item: Donation }) => (
    <Card variant="outlined" style={styles.donationCard}>
      <View style={styles.donationHeader}>
        <View style={styles.donationInfo}>
          <ThemedText type="defaultSemiBold" style={styles.donationAmount}>
            {formatCurrency(item.amount)}
          </ThemedText>
          <ThemedText style={styles.donationCampaign}>
            {item.campaign || 'General Fund'}
          </ThemedText>
          <ThemedText style={styles.donationDate}>
            {item.createdAt.toLocaleDateString()}
          </ThemedText>
        </View>
        
        <View style={styles.donationStatus}>
          <View style={[
            styles.statusBadge,
            item.status === 'completed' && styles.statusCompleted,
            item.status === 'pending' && styles.statusPending,
            item.status === 'failed' && styles.statusFailed,
          ]}>
            <ThemedText style={[
              styles.statusBadgeText,
              item.status === 'completed' && styles.statusCompletedText,
              item.status === 'pending' && styles.statusPendingText,
              item.status === 'failed' && styles.statusFailedText,
            ]}>
              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </ThemedText>
          </View>
        </View>
      </View>
      
      <View style={styles.donationDetails}>
        <ThemedText style={styles.donationMethod}>
          Payment: {item.paymentMethod}
        </ThemedText>
        {item.frequency !== DonationFrequency.ONE_TIME && (
          <ThemedText style={styles.donationFrequency}>
            Frequency: {item.frequency.replace('_', ' ').toLowerCase()}
          </ThemedText>
        )}
      </View>
    </Card>
  );

  const renderImpactStats = () => (
    <View style={styles.impactContainer}>
      <Card variant="elevated" style={styles.impactCard}>
        <ThemedText type="subtitle" style={styles.impactTitle}>
          Your Impact
        </ThemedText>
        
        <View style={styles.impactStats}>
          <View style={styles.impactStat}>
            <ThemedText style={styles.impactNumber}>
              {formatCurrency(donationHistory.reduce((sum, d) => sum + d.amount, 0))}
            </ThemedText>
            <ThemedText style={styles.impactLabel}>Total Donated</ThemedText>
          </View>
          
          <View style={styles.impactStat}>
            <ThemedText style={styles.impactNumber}>
              {donationHistory.length}
            </ThemedText>
            <ThemedText style={styles.impactLabel}>Donations Made</ThemedText>
          </View>
          
          <View style={styles.impactStat}>
            <ThemedText style={styles.impactNumber}>3</ThemedText>
            <ThemedText style={styles.impactLabel}>Students Helped</ThemedText>
          </View>
        </View>
      </Card>
      
      <Card variant="elevated" style={styles.impactCard}>
        <ThemedText type="subtitle" style={styles.impactTitle}>
          Community Impact
        </ThemedText>
        
        <View style={styles.communityStats}>
          <View style={styles.communityStat}>
            <ThemedText style={styles.communityNumber}>R 2.5M</ThemedText>
            <ThemedText style={styles.communityLabel}>Total Raised This Year</ThemedText>
          </View>
          
          <View style={styles.communityStat}>
            <ThemedText style={styles.communityNumber}>150</ThemedText>
            <ThemedText style={styles.communityLabel}>Students Supported</ThemedText>
          </View>
          
          <View style={styles.communityStat}>
            <ThemedText style={styles.communityNumber}>5</ThemedText>
            <ThemedText style={styles.communityLabel}>Active Campaigns</ThemedText>
          </View>
        </View>
      </Card>
    </View>
  );

  const renderEmptyState = () => (
    <Card variant="outlined" style={styles.emptyState}>
      <ThemedText style={styles.emptyTitle}>
        {activeTab === 'campaigns' && 'No active campaigns'}
        {activeTab === 'history' && 'No donation history'}
        {activeTab === 'impact' && 'Start making an impact'}
      </ThemedText>
      <ThemedText style={styles.emptyDescription}>
        {activeTab === 'campaigns' && 'Check back later for new fundraising campaigns.'}
        {activeTab === 'history' && 'Your donation history will appear here after you make your first donation.'}
        {activeTab === 'impact' && 'Make your first donation to see your impact on the PROTEC community.'}
      </ThemedText>
      <Button
        title="Make a Donation"
        onPress={() => handleDonate()}
        style={styles.emptyButton}
      />
    </Card>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <ThemedText type="title">Donations</ThemedText>
          <ThemedText style={styles.subtitle}>
            Support PROTEC's mission and future students
          </ThemedText>
        </View>
        
        <TouchableOpacity
          style={styles.quickDonateButton}
          onPress={handleQuickDonate}
        >
          <ThemedText style={styles.quickDonateText}>💝 Quick Donate</ThemedText>
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'campaigns' && styles.activeTab]}
          onPress={() => setActiveTab('campaigns')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'campaigns' && styles.activeTabText
          ]}>
            Campaigns
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={() => setActiveTab('history')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'history' && styles.activeTabText
          ]}>
            My History
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'impact' && styles.activeTab]}
          onPress={() => setActiveTab('impact')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'impact' && styles.activeTabText
          ]}>
            Impact
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {isLoading ? (
        <LoadingSpinner text="Loading donations..." />
      ) : (
        <View style={styles.content}>
          {activeTab === 'campaigns' && (
            <FlatList
              data={campaigns}
              renderItem={renderCampaignItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
              }
              ListEmptyComponent={() => renderEmptyState()}
              contentContainerStyle={styles.listContent}
            />
          )}

          {activeTab === 'history' && (
            <FlatList
              data={donationHistory}
              renderItem={renderDonationItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
              }
              ListEmptyComponent={() => renderEmptyState()}
              contentContainerStyle={styles.listContent}
            />
          )}

          {activeTab === 'impact' && (
            <ScrollView
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
              }
              contentContainerStyle={styles.listContent}
            >
              {donationHistory.length > 0 ? renderImpactStats() : renderEmptyState()}
            </ScrollView>
          )}
        </View>
      )}

      {/* Quick Donate Modal */}
      <QuickDonateModal
        visible={showQuickDonateModal}
        onClose={() => setShowQuickDonateModal(false)}
        onDonate={handleDonate}
      />
    </ThemedView>
  );
}

// Quick Donate Modal Component
interface QuickDonateModalProps {
  visible: boolean;
  onClose: () => void;
  onDonate: (campaignId?: string) => void;
}

function QuickDonateModal({ visible, onClose, onDonate }: QuickDonateModalProps) {
  const quickAmounts = [100, 250, 500, 1000, 2500, 5000];

  const handleQuickAmount = (amount: number) => {
    onClose();
    // TODO: Navigate to donation form with pre-filled amount
    onDonate();
  };

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      title="Quick Donate"
      size="md"
    >
      <View style={styles.quickDonateContent}>
        <ThemedText style={styles.quickDonateDescription}>
          Choose an amount to make a quick donation to PROTEC's general fund.
        </ThemedText>

        <View style={styles.quickAmountsGrid}>
          {quickAmounts.map((amount) => (
            <TouchableOpacity
              key={amount}
              style={styles.quickAmountButton}
              onPress={() => handleQuickAmount(amount)}
            >
              <ThemedText style={styles.quickAmountText}>
                R{amount.toLocaleString()}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>

        <Button
          title="Custom Amount"
          onPress={() => {
            onClose();
            onDonate();
          }}
          variant="outline"
          style={styles.customAmountButton}
        />
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 16,
    paddingBottom: 8,
  },
  headerContent: {
    flex: 1,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  quickDonateButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  quickDonateText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  tabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#012A5B',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#012A5B',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  campaignCard: {
    marginBottom: 16,
  },
  campaignHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  campaignTitle: {
    flex: 1,
    marginRight: 12,
  },
  campaignStatus: {
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: '#22c55e',
    fontWeight: '500',
  },
  campaignDescription: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
    marginBottom: 16,
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#012A5B',
    borderRadius: 4,
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressText: {
    fontSize: 14,
    color: '#666',
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '600',
    color: '#012A5B',
  },
  campaignActions: {
    flexDirection: 'row',
    gap: 12,
  },
  donateButton: {
    flex: 1,
  },
  learnMoreButton: {
    flex: 1,
  },
  donationCard: {
    marginBottom: 12,
  },
  donationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  donationInfo: {
    flex: 1,
  },
  donationAmount: {
    fontSize: 18,
    color: '#012A5B',
    marginBottom: 4,
  },
  donationCampaign: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  donationDate: {
    fontSize: 12,
    color: '#888',
  },
  donationStatus: {
    marginLeft: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusCompleted: {
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
  },
  statusPending: {
    backgroundColor: 'rgba(251, 191, 36, 0.1)',
  },
  statusFailed: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  statusCompletedText: {
    color: '#22c55e',
  },
  statusPendingText: {
    color: '#f59e0b',
  },
  statusFailedText: {
    color: '#ef4444',
  },
  donationDetails: {
    gap: 2,
  },
  donationMethod: {
    fontSize: 12,
    color: '#888',
  },
  donationFrequency: {
    fontSize: 12,
    color: '#888',
  },
  impactContainer: {
    gap: 16,
  },
  impactCard: {
    marginBottom: 0,
  },
  impactTitle: {
    marginBottom: 16,
  },
  impactStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  impactStat: {
    alignItems: 'center',
  },
  impactNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#012A5B',
    marginBottom: 4,
  },
  impactLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  communityStats: {
    gap: 16,
  },
  communityStat: {
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  communityNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginBottom: 4,
  },
  communityLabel: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
  quickDonateContent: {
    alignItems: 'center',
  },
  quickDonateDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  quickAmountsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 24,
    justifyContent: 'center',
  },
  quickAmountButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#012A5B',
    backgroundColor: 'rgba(1, 42, 91, 0.05)',
    minWidth: 100,
    alignItems: 'center',
  },
  quickAmountText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#012A5B',
  },
  customAmountButton: {
    width: '100%',
  },
});
