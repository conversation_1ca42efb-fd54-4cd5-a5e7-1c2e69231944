import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, Card, LoadingSpinner } from '@/components/ui';
import { useAuthStore } from '@/lib/stores/authStore';
import { Post } from '@/lib/types';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    FlatList,
    RefreshControl,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';

export default function FeedScreen() {
  const [activeTab, setActiveTab] = useState<'all' | 'following' | 'my'>('all');
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const { user, isAuthenticated } = useAuthStore();

  // Mock data - replace with actual API calls
  const [posts, setPosts] = useState<Post[]>([
    {
      id: '1',
      author: {
        id: '1',
        email: '<EMAIL>',
        name: '<PERSON>',
        graduationYear: 2020,
        programmes: ['<PERSON> Science'],
        currentRole: 'Software Engineer',
        company: 'Tech Corp',
        industry: 'Technology',
        skills: ['JavaScript', 'React'],
        interests: ['AI'],
        province: 'Gauteng',
        city: 'Johannesburg',
        country: 'South Africa',
        role: 'ALUMNI' as const,
        isActive: true,
      },
      authorId: '1',
      content: 'Excited to share that I just got promoted to Senior Software Engineer! 🎉 Thank you PROTEC for giving me the foundation to succeed in tech.',
      mediaUrls: [],
      tags: ['career', 'promotion', 'tech'],
      likes: ['2', '3'],
      comments: [],
      isModerated: true,
      isPublic: true,
      isPinned: false,
      createdAt: new Date('2024-01-15T10:30:00'),
      updatedAt: new Date('2024-01-15T10:30:00'),
    },
  ]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // TODO: Implement API call to refresh posts
      await new Promise(resolve => setTimeout(resolve, 1000));
    } finally {
      setRefreshing(false);
    }
  };

  const handleCreatePost = () => {
    if (!isAuthenticated) {
      Alert.alert(
        'Login Required',
        'Please log in to create posts.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Login', onPress: () => router.push('/auth/login') },
        ]
      );
      return;
    }

    router.push('/posts/create');
  };

  const getDisplayedPosts = () => {
    switch (activeTab) {
      case 'following':
        // TODO: Filter posts from followed users
        return posts;
      case 'my':
        return posts.filter(post => post.authorId === user?.id);
      default:
        return posts;
    }
  };

  const displayedPosts = getDisplayedPosts();

  const renderPostItem = ({ item }: { item: Post }) => (
    <PostCard post={item} />
  );

  const renderEmptyState = () => (
    <Card variant="outlined" style={styles.emptyState}>
      <ThemedText style={styles.emptyTitle}>
        {activeTab === 'all' && 'No posts yet'}
        {activeTab === 'following' && 'No posts from people you follow'}
        {activeTab === 'my' && 'You haven\'t posted anything yet'}
      </ThemedText>
      <ThemedText style={styles.emptyDescription}>
        {activeTab === 'all' && 'Be the first to share something with the community!'}
        {activeTab === 'following' && 'Follow alumni to see their posts here.'}
        {activeTab === 'my' && 'Share your achievements and updates with fellow alumni.'}
      </ThemedText>
      {(activeTab === 'all' || activeTab === 'my') && (
        <Button
          title="Create Post"
          onPress={handleCreatePost}
          style={styles.emptyButton}
        />
      )}
      {activeTab === 'following' && (
        <Button
          title="Browse Alumni"
          onPress={() => router.push('/(tabs)/directory')}
          variant="outline"
          style={styles.emptyButton}
        />
      )}
    </Card>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <ThemedText type="title">Feed</ThemedText>
          <ThemedText style={styles.subtitle}>
            Stay connected with the PROTEC community
          </ThemedText>
        </View>

        <TouchableOpacity
          style={styles.createButton}
          onPress={handleCreatePost}
        >
          <ThemedText style={styles.createButtonText}>+ Post</ThemedText>
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'all' && styles.activeTab]}
          onPress={() => setActiveTab('all')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'all' && styles.activeTabText
          ]}>
            All Posts
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'following' && styles.activeTab]}
          onPress={() => setActiveTab('following')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'following' && styles.activeTabText
          ]}>
            Following
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'my' && styles.activeTab]}
          onPress={() => setActiveTab('my')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'my' && styles.activeTabText
          ]}>
            My Posts
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* Posts List */}
      {isLoading ? (
        <LoadingSpinner text="Loading posts..." />
      ) : (
        <FlatList
          data={displayedPosts}
          renderItem={renderPostItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}
    </ThemedView>
  );
}

// PostCard Component
interface PostCardProps {
  post: Post;
}

function PostCard({ post }: PostCardProps) {
  const { user } = useAuthStore();
  const [isLiked, setIsLiked] = useState(post.likes.includes(user?.id || ''));
  const [likesCount, setLikesCount] = useState(post.likes.length);
  const [showComments, setShowComments] = useState(false);

  const handleLike = () => {
    // TODO: Implement API call to like/unlike post
    if (isLiked) {
      setLikesCount(prev => prev - 1);
      setIsLiked(false);
    } else {
      setLikesCount(prev => prev + 1);
      setIsLiked(true);
    }
  };

  const handleComment = () => {
    setShowComments(!showComments);
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    Alert.alert('Share', 'Share functionality coming soon!');
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return date.toLocaleDateString();
  };

  return (
    <Card variant="elevated" style={styles.postCard}>
      {/* Post Header */}
      <View style={styles.postHeader}>
        <TouchableOpacity
          style={styles.authorInfo}
          onPress={() => router.push(`/alumni/${post.author.id}`)}
        >
          <View style={styles.authorAvatar}>
            <ThemedText style={styles.avatarText}>
              {post.author.name.charAt(0).toUpperCase()}
            </ThemedText>
          </View>

          <View style={styles.authorDetails}>
            <ThemedText type="defaultSemiBold" style={styles.authorName}>
              {post.author.name}
            </ThemedText>
            <ThemedText style={styles.authorMeta}>
              {post.author.currentRole} • {formatTimeAgo(post.createdAt)}
            </ThemedText>
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.postMenu}>
          <ThemedText style={styles.menuIcon}>⋯</ThemedText>
        </TouchableOpacity>
      </View>

      {/* Post Content */}
      <ThemedText style={styles.postContent}>
        {post.content}
      </ThemedText>

      {/* Post Tags */}
      {post.tags && post.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {post.tags.map((tag, index) => (
            <View key={index} style={styles.tag}>
              <ThemedText style={styles.tagText}>#{tag}</ThemedText>
            </View>
          ))}
        </View>
      )}

      {/* Post Actions */}
      <View style={styles.postActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleLike}
        >
          <ThemedText style={[styles.actionIcon, isLiked && styles.likedIcon]}>
            {isLiked ? '❤️' : '🤍'}
          </ThemedText>
          <ThemedText style={[styles.actionText, isLiked && styles.likedText]}>
            {likesCount}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleComment}
        >
          <ThemedText style={styles.actionIcon}>💬</ThemedText>
          <ThemedText style={styles.actionText}>
            {post.comments.length}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleShare}
        >
          <ThemedText style={styles.actionIcon}>📤</ThemedText>
          <ThemedText style={styles.actionText}>Share</ThemedText>
        </TouchableOpacity>
      </View>

      {/* Comments Section */}
      {showComments && (
        <View style={styles.commentsSection}>
          <View style={styles.commentsDivider} />
          {post.comments.length === 0 ? (
            <ThemedText style={styles.noComments}>
              No comments yet. Be the first to comment!
            </ThemedText>
          ) : (
            post.comments.map((comment) => (
              <View key={comment.id} style={styles.comment}>
                <ThemedText type="defaultSemiBold" style={styles.commentAuthor}>
                  {comment.author.name}
                </ThemedText>
                <ThemedText style={styles.commentText}>
                  {comment.text}
                </ThemedText>
                <ThemedText style={styles.commentTime}>
                  {formatTimeAgo(comment.createdAt)}
                </ThemedText>
              </View>
            ))
          )}
        </View>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 16,
    paddingBottom: 8,
  },
  headerContent: {
    flex: 1,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  createButton: {
    backgroundColor: '#012A5B',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  tabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#012A5B',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#012A5B',
    fontWeight: '600',
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
  postCard: {
    marginBottom: 16,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  authorAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#012A5B',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  authorDetails: {
    flex: 1,
  },
  authorName: {
    fontSize: 16,
    marginBottom: 2,
  },
  authorMeta: {
    fontSize: 12,
    color: '#666',
  },
  postMenu: {
    padding: 8,
  },
  menuIcon: {
    fontSize: 16,
    color: '#666',
  },
  postContent: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 12,
  },
  tag: {
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  actionIcon: {
    fontSize: 16,
  },
  actionText: {
    fontSize: 14,
    color: '#666',
  },
  likedIcon: {
    color: '#e74c3c',
  },
  likedText: {
    color: '#e74c3c',
    fontWeight: '500',
  },
  commentsSection: {
    marginTop: 12,
  },
  commentsDivider: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginBottom: 12,
  },
  noComments: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 16,
  },
  comment: {
    marginBottom: 12,
    paddingLeft: 12,
    borderLeftWidth: 3,
    borderLeftColor: 'rgba(1, 42, 91, 0.2)',
  },
  commentAuthor: {
    fontSize: 14,
    marginBottom: 4,
  },
  commentText: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 4,
  },
  commentTime: {
    fontSize: 12,
    color: '#888',
  },
});
