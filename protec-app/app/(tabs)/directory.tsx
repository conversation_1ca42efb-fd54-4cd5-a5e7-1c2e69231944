import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { AlumniCard, Button, Card, Input, LoadingSpinner, Modal } from '@/components/ui';
import { useAlumni, useSearchAlumni } from '@/lib/hooks/useAlumni';
import { useAlumniStore } from '@/lib/stores/alumniStore';
import { Alumni } from '@/lib/types';
import React, { useEffect, useState } from 'react';
import {
    FlatList,
    RefreshControl,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

export default function DirectoryScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const {
    alumni,
    filters,
    isLoading,
    setSearchQuery: setStoreSearchQuery,
    setFilters,
    clearFilters,
  } = useAlumniStore();

  const { data: alumniData, refetch } = useAlumni(filters);
  const { data: searchResults } = useSearchAlumni(searchQuery);

  const displayedAlumni = searchQuery.length > 2 ? searchResults : alumni;

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setStoreSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, setStoreSearchQuery]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters({ [key]: value });
  };

  const handleClearFilters = () => {
    clearFilters();
    setSearchQuery('');
  };

  const renderAlumniItem = ({ item }: { item: Alumni }) => (
    <AlumniCard alumni={item} />
  );

  const renderEmptyState = () => (
    <Card variant="outlined" style={styles.emptyState}>
      <ThemedText style={styles.emptyTitle}>
        {searchQuery ? 'No alumni found' : 'No alumni to display'}
      </ThemedText>
      <ThemedText style={styles.emptyDescription}>
        {searchQuery
          ? `No alumni match your search for "${searchQuery}"`
          : 'Try adjusting your filters or check back later'}
      </ThemedText>
      {(searchQuery || Object.keys(filters).length > 0) && (
        <Button
          title="Clear Search & Filters"
          onPress={handleClearFilters}
          variant="outline"
          style={styles.emptyButton}
        />
      )}
    </Card>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <ThemedText type="title">Alumni Directory</ThemedText>
        <ThemedText style={styles.subtitle}>
          Connect with {alumni.length} PROTEC alumni worldwide
        </ThemedText>
      </View>

      {/* Search and Filters */}
      <View style={styles.searchContainer}>
        <Input
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search alumni by name, company, or skills..."
          style={styles.searchInput}
          containerStyle={styles.searchInputContainer}
        />

        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilters(true)}
        >
          <ThemedText style={styles.filterButtonText}>
            Filters {Object.keys(filters).length > 0 && `(${Object.keys(filters).length})`}
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* Active Filters */}
      {Object.keys(filters).length > 0 && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.activeFilters}
          contentContainerStyle={styles.activeFiltersContent}
        >
          {Object.entries(filters).map(([key, value]) => (
            <TouchableOpacity
              key={key}
              style={styles.activeFilter}
              onPress={() => handleFilterChange(key, undefined)}
            >
              <ThemedText style={styles.activeFilterText}>
                {key}: {Array.isArray(value) ? value.join(', ') : value} ×
              </ThemedText>
            </TouchableOpacity>
          ))}
          <TouchableOpacity
            style={styles.clearFiltersButton}
            onPress={handleClearFilters}
          >
            <ThemedText style={styles.clearFiltersText}>Clear All</ThemedText>
          </TouchableOpacity>
        </ScrollView>
      )}

      {/* Alumni List */}
      {isLoading ? (
        <LoadingSpinner text="Loading alumni..." />
      ) : (
        <FlatList
          data={displayedAlumni || []}
          renderItem={renderAlumniItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}

      {/* Filters Modal */}
      <FiltersModal
        visible={showFilters}
        onClose={() => setShowFilters(false)}
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
      />
    </ThemedView>
  );
}

interface FiltersModalProps {
  visible: boolean;
  onClose: () => void;
  filters: any;
  onFilterChange: (key: string, value: any) => void;
  onClearFilters: () => void;
}

function FiltersModal({
  visible,
  onClose,
  filters,
  onFilterChange,
  onClearFilters,
}: FiltersModalProps) {
  const [localFilters, setLocalFilters] = useState(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleApplyFilters = () => {
    Object.entries(localFilters).forEach(([key, value]) => {
      onFilterChange(key, value);
    });
    onClose();
  };

  const handleLocalFilterChange = (key: string, value: any) => {
    setLocalFilters((prev: any) => ({ ...prev, [key]: value }));
  };

  const graduationYears = Array.from({ length: 30 }, (_, i) => 2024 - i);
  const industries = [
    'Technology',
    'Finance',
    'Healthcare',
    'Education',
    'Engineering',
    'Consulting',
    'Government',
    'Non-profit',
    'Other',
  ];
  const provinces = [
    'Gauteng',
    'Western Cape',
    'KwaZulu-Natal',
    'Eastern Cape',
    'Free State',
    'Limpopo',
    'Mpumalanga',
    'North West',
    'Northern Cape',
  ];

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      title="Filter Alumni"
      size="lg"
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Graduation Year */}
        <View style={styles.filterSection}>
          <ThemedText style={styles.filterLabel}>Graduation Year</ThemedText>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.filterOptions}
          >
            {graduationYears.map((year) => (
              <TouchableOpacity
                key={year}
                style={[
                  styles.filterOption,
                  localFilters.graduationYear === year && styles.filterOptionSelected,
                ]}
                onPress={() =>
                  handleLocalFilterChange(
                    'graduationYear',
                    localFilters.graduationYear === year ? undefined : year
                  )
                }
              >
                <ThemedText
                  style={[
                    styles.filterOptionText,
                    localFilters.graduationYear === year && styles.filterOptionTextSelected,
                  ]}
                >
                  {year}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Industry */}
        <View style={styles.filterSection}>
          <ThemedText style={styles.filterLabel}>Industry</ThemedText>
          <View style={styles.filterGrid}>
            {industries.map((industry) => (
              <TouchableOpacity
                key={industry}
                style={[
                  styles.filterOption,
                  localFilters.industry === industry && styles.filterOptionSelected,
                ]}
                onPress={() =>
                  handleLocalFilterChange(
                    'industry',
                    localFilters.industry === industry ? undefined : industry
                  )
                }
              >
                <ThemedText
                  style={[
                    styles.filterOptionText,
                    localFilters.industry === industry && styles.filterOptionTextSelected,
                  ]}
                >
                  {industry}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Location */}
        <View style={styles.filterSection}>
          <ThemedText style={styles.filterLabel}>Province</ThemedText>
          <View style={styles.filterGrid}>
            {provinces.map((province) => (
              <TouchableOpacity
                key={province}
                style={[
                  styles.filterOption,
                  localFilters.location === province && styles.filterOptionSelected,
                ]}
                onPress={() =>
                  handleLocalFilterChange(
                    'location',
                    localFilters.location === province ? undefined : province
                  )
                }
              >
                <ThemedText
                  style={[
                    styles.filterOptionText,
                    localFilters.location === province && styles.filterOptionTextSelected,
                  ]}
                >
                  {province}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Modal Actions */}
      <View style={styles.modalActions}>
        <Button
          title="Clear All"
          onPress={() => {
            setLocalFilters({});
            onClearFilters();
          }}
          variant="outline"
          style={styles.modalButton}
        />
        <Button
          title="Apply Filters"
          onPress={handleApplyFilters}
          style={styles.modalButton}
        />
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 8,
    gap: 12,
  },
  searchInputContainer: {
    flex: 1,
    marginBottom: 0,
  },
  searchInput: {
    marginBottom: 0,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#012A5B',
    justifyContent: 'center',
    minWidth: 80,
  },
  filterButtonText: {
    color: '#012A5B',
    fontWeight: '500',
    textAlign: 'center',
  },
  activeFilters: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  activeFiltersContent: {
    gap: 8,
  },
  activeFilter: {
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  activeFilterText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  clearFiltersButton: {
    backgroundColor: 'rgba(220, 38, 38, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  clearFiltersText: {
    fontSize: 12,
    color: '#dc2626',
    fontWeight: '500',
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
  filterSection: {
    marginBottom: 24,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  filterOptions: {
    flexDirection: 'row',
  },
  filterGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    marginRight: 8,
    marginBottom: 8,
  },
  filterOptionSelected: {
    backgroundColor: '#012A5B',
    borderColor: '#012A5B',
  },
  filterOptionText: {
    fontSize: 14,
    color: '#666',
  },
  filterOptionTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  modalButton: {
    flex: 1,
  },
});
