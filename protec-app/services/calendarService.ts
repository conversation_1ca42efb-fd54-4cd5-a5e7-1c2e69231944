import * as Calendar from 'expo-calendar';
import { Alert, Platform } from 'react-native';
import { eventsService } from '../lib/services/eventsService';

export interface AlumniCalendarEvent {
  id?: string;
  title: string;
  startDate: Date;
  endDate: Date;
  location?: string;
  notes?: string;
  allDay?: boolean;
  recurrenceRule?: Calendar.RecurrenceRule;
  alarms?: Calendar.Alarm[];
  url?: string;
  organizer?: string;
  attendees?: string[];
}

export interface CalendarInfo {
  id: string;
  title: string;
  color: string;
  entityType: Calendar.EntityTypes;
  sourceId?: string;
  source: Calendar.Source;
  type: Calendar.CalendarType;
  allowsModifications: boolean;
  allowedAvailabilities: Calendar.Availability[];
}

export class CalendarService {
  /**
   * Request calendar permissions
   */
  static async requestPermission(): Promise<boolean> {
    try {
      const { status } = await Calendar.requestCalendarPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting calendar permission:', error);
      return false;
    }
  }

  /**
   * Check if calendar permission is granted
   */
  static async hasPermission(): Promise<boolean> {
    try {
      const { status } = await Calendar.getCalendarPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking calendar permission:', error);
      return false;
    }
  }

  /**
   * Get all available calendars
   */
  static async getCalendars(): Promise<CalendarInfo[]> {
    try {
      const hasPermission = await this.hasPermission();
      if (!hasPermission) {
        const granted = await this.requestPermission();
        if (!granted) {
          Alert.alert(
            'Calendar Permission Required',
            'Please grant calendar permission to save alumni events.',
            [{ text: 'OK' }]
          );
          return [];
        }
      }

      const calendars = await Calendar.getCalendarsAsync(Calendar.EntityTypes.EVENT);
      return calendars.map(calendar => ({
        id: calendar.id,
        title: calendar.title,
        color: calendar.color,
        entityType: calendar.entityType || Calendar.EntityTypes.EVENT,
        sourceId: calendar.sourceId,
        source: calendar.source,
        type: calendar.type || Calendar.CalendarType.LOCAL,
        allowsModifications: calendar.allowsModifications,
        allowedAvailabilities: calendar.allowedAvailabilities,
      }));
    } catch (error) {
      console.error('Error getting calendars:', error);
      return [];
    }
  }

  /**
   * Get the default calendar for adding events
   */
  static async getDefaultCalendar(): Promise<CalendarInfo | null> {
    try {
      const calendars = await this.getCalendars();
      
      // Find the first writable calendar
      const writableCalendar = calendars.find(cal => cal.allowsModifications);
      
      if (writableCalendar) {
        return writableCalendar;
      }

      // If no writable calendar found, try to create one
      return await this.createAlumniCalendar();
    } catch (error) {
      console.error('Error getting default calendar:', error);
      return null;
    }
  }

  /**
   * Create a dedicated PROTEC Alumni calendar
   */
  static async createAlumniCalendar(): Promise<CalendarInfo | null> {
    try {
      const calendars = await this.getCalendars();
      
      // Check if PROTEC Alumni calendar already exists
      const existingCalendar = calendars.find(cal => 
        cal.title === 'PROTEC Alumni Events'
      );
      
      if (existingCalendar) {
        return existingCalendar;
      }

      // Get the default source for creating calendars
      let defaultSource;
      if (Platform.OS === 'ios') {
        defaultSource = await Calendar.getDefaultCalendarAsync();
      } else {
        defaultSource = undefined; // Will use default source
      }

      if (!defaultSource) {
        console.error('No default calendar source available');
        return null;
      }

      const calendarId = await Calendar.createCalendarAsync({
        title: 'PROTEC Alumni Events',
        color: '#012A5B', // PROTEC brand color
        entityType: Calendar.EntityTypes.EVENT,
        sourceId: Platform.OS === 'ios' ? defaultSource.source.id : undefined,
        source: Platform.OS === 'ios' ? (defaultSource as any)?.source : defaultSource,
        name: 'PROTEC Alumni Events',
        ownerAccount: 'personal',
        accessLevel: Calendar.CalendarAccessLevel.OWNER,
      });

      // Return the created calendar info
      const newCalendars = await this.getCalendars();
      return newCalendars.find(cal => cal.id === calendarId) || null;
    } catch (error) {
      console.error('Error creating alumni calendar:', error);
      return null;
    }
  }

  /**
   * Add an alumni event to calendar
   */
  static async addEvent(event: AlumniCalendarEvent, calendarId?: string): Promise<string | null> {
    try {
      const hasPermission = await this.hasPermission();
      if (!hasPermission) {
        const granted = await this.requestPermission();
        if (!granted) {
          Alert.alert(
            'Calendar Permission Required',
            'Please grant calendar permission to save alumni events.',
            [{ text: 'OK' }]
          );
          return null;
        }
      }

      let targetCalendarId = calendarId;
      if (!targetCalendarId) {
        const defaultCalendar = await this.getDefaultCalendar();
        if (!defaultCalendar) {
          Alert.alert('Error', 'No writable calendar found.');
          return null;
        }
        targetCalendarId = defaultCalendar.id;
      }

      const eventDetails = {
        title: event.title,
        startDate: event.startDate,
        endDate: event.endDate,
        location: event.location || null,
        notes: event.notes || '',
        allDay: event.allDay || false,
        recurrenceRule: event.recurrenceRule || null,
        alarms: event.alarms || [
          { relativeOffset: -60 * 24 }, // 1 day before
          { relativeOffset: -60 }, // 1 hour before
        ],
        url: event.url,
        timeZone: 'Africa/Johannesburg',
        availability: Calendar.Availability.BUSY,
        status: Calendar.EventStatus.CONFIRMED,
      };

      const eventId = await Calendar.createEventAsync(targetCalendarId, eventDetails);
      return eventId;
    } catch (error) {
      console.error('Error adding event to calendar:', error);
      Alert.alert('Error', 'Failed to add event to calendar. Please try again.');
      return null;
    }
  }

  /**
   * Update an existing calendar event
   */
  static async updateEvent(eventId: string, updates: Partial<AlumniCalendarEvent>): Promise<boolean> {
    try {
      const hasPermission = await this.hasPermission();
      if (!hasPermission) {
        return false;
      }

      const eventDetails: Partial<Calendar.Event> = {};
      
      if (updates.title) eventDetails.title = updates.title;
      if (updates.startDate) eventDetails.startDate = updates.startDate;
      if (updates.endDate) eventDetails.endDate = updates.endDate;
      if (updates.location) eventDetails.location = updates.location;
      if (updates.notes) eventDetails.notes = updates.notes;
      if (updates.allDay !== undefined) eventDetails.allDay = updates.allDay;
      if (updates.recurrenceRule) eventDetails.recurrenceRule = updates.recurrenceRule;
      if (updates.alarms) eventDetails.alarms = updates.alarms;
      if (updates.url) eventDetails.url = updates.url;
      // Note: organizer field requires specific format, skipping for now

      await Calendar.updateEventAsync(eventId, eventDetails);
      return true;
    } catch (error) {
      console.error('Error updating calendar event:', error);
      Alert.alert('Error', 'Failed to update calendar event. Please try again.');
      return false;
    }
  }

  /**
   * Delete a calendar event
   */
  static async deleteEvent(eventId: string): Promise<boolean> {
    try {
      const hasPermission = await this.hasPermission();
      if (!hasPermission) {
        return false;
      }

      await Calendar.deleteEventAsync(eventId);
      return true;
    } catch (error) {
      console.error('Error deleting calendar event:', error);
      Alert.alert('Error', 'Failed to delete calendar event. Please try again.');
      return false;
    }
  }

  /**
   * Get events from calendar within a date range
   */
  static async getEvents(
    startDate: Date,
    endDate: Date,
    calendarIds?: string[]
  ): Promise<Calendar.Event[]> {
    try {
      const hasPermission = await this.hasPermission();
      if (!hasPermission) {
        const granted = await this.requestPermission();
        if (!granted) {
          return [];
        }
      }

      let targetCalendarIds = calendarIds;
      if (!targetCalendarIds) {
        const calendars = await this.getCalendars();
        targetCalendarIds = calendars.map(cal => cal.id);
      }

      const events = await Calendar.getEventsAsync(targetCalendarIds, startDate, endDate);
      return events;
    } catch (error) {
      console.error('Error getting calendar events:', error);
      return [];
    }
  }

  /**
   * Create a quick alumni event with default settings
   */
  static async createQuickAlumniEvent(
    title: string,
    startDate: Date,
    durationHours: number = 2,
    location?: string,
    description?: string
  ): Promise<string | null> {
    const endDate = new Date(startDate.getTime() + durationHours * 60 * 60 * 1000);
    
    const event: AlumniCalendarEvent = {
      title: `PROTEC Alumni: ${title}`,
      startDate,
      endDate,
      location,
      notes: description ? `${description}\n\nOrganized by PROTEC Alumni Network` : 'Organized by PROTEC Alumni Network',
      alarms: [
        { relativeOffset: -60 * 24 }, // 1 day before
        { relativeOffset: -60 * 2 }, // 2 hours before
        { relativeOffset: -15 }, // 15 minutes before
      ],
    };

    return await this.addEvent(event);
  }

  /**
   * Open calendar app to show a specific date
   */
  static async openCalendarApp(date?: Date): Promise<void> {
    try {
      const targetDate = date || new Date();
      
      if (Platform.OS === 'ios') {
        // iOS calendar URL scheme
        const calendarUrl = `calshow:${Math.floor(targetDate.getTime() / 1000)}`;
        // Note: You would need to use Linking.openURL(calendarUrl) here
        // but we're keeping this service focused on Calendar functionality
        console.log('Open calendar URL:', calendarUrl);
      } else {
        // Android calendar intent
        console.log('Open Android calendar for date:', targetDate);
      }
    } catch (error) {
      console.error('Error opening calendar app:', error);
    }
  }

  /**
   * Get upcoming alumni events from the events service
   */
  static async getUpcomingAlumniEvents(daysAhead: number = 30): Promise<AlumniCalendarEvent[]> {
    try {
      const upcomingEvents = await eventsService.getUpcomingEvents(10);

      return upcomingEvents.map(event => ({
        id: event.id,
        title: event.title,
        startDate: new Date(event.startDate),
        endDate: new Date(event.endDate),
        location: event.location || (event.isVirtual ? 'Virtual Event' : undefined),
        notes: event.description,
        allDay: event.allDay || false,
        isAlumniEvent: true,
        eventType: event.category || 'general',
        url: event.isVirtual ? event.virtualLink : undefined,
      }));
    } catch (error) {
      console.error('Error getting upcoming alumni events:', error);
      return [];
    }
  }

  /**
   * Sync alumni events to device calendar
   */
  static async syncAlumniEventsToCalendar(): Promise<void> {
    try {
      const hasPermission = await this.hasPermission();
      if (!hasPermission) {
        const granted = await this.requestPermission();
        if (!granted) {
          throw new Error('Calendar permission required to sync events');
        }
      }

      // Get or create PROTEC Alumni calendar
      let alumniCalendar = await this.getAlumniCalendar();
      if (!alumniCalendar) {
        alumniCalendar = await this.createAlumniCalendar();
      }

      // Get upcoming alumni events
      const alumniEvents = await eventsService.getUpcomingEvents(50);

      // Add events to calendar
      for (const event of alumniEvents) {
        try {
          await this.addEventToCalendar({
            title: event.title,
            startDate: new Date(event.startDate),
            endDate: new Date(event.endDate),
            location: event.location || (event.isVirtual ? 'Virtual Event' : undefined),
            notes: `${event.description}\n\nEvent ID: ${event.id}`,
            allDay: event.allDay || false,
            url: event.isVirtual ? event.virtualLink : undefined,
            alarms: [
              { relativeOffset: -60 * 24 }, // 1 day before
              { relativeOffset: -60 }, // 1 hour before
            ],
          }, alumniCalendar.id);
        } catch (eventError) {
          console.error(`Failed to add event ${event.id} to calendar:`, eventError);
        }
      }

      Alert.alert(
        'Sync Complete',
        `Successfully synced ${alumniEvents.length} alumni events to your calendar.`
      );
    } catch (error) {
      console.error('Error syncing alumni events:', error);
      Alert.alert(
        'Sync Failed',
        'Failed to sync alumni events to calendar. Please try again.'
      );
    }
  }

  /**
   * Get or find the PROTEC Alumni calendar
   */
  static async getAlumniCalendar(): Promise<Calendar.Calendar | null> {
    try {
      const calendars = await this.getCalendars();
      return calendars.find(cal => cal.title === 'PROTEC Alumni Events') || null;
    } catch (error) {
      console.error('Error finding alumni calendar:', error);
      return null;
    }
  }
}

export default CalendarService;
