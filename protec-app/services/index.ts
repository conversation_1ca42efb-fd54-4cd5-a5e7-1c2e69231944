// Export all services for easy importing
export { default as ContactsService } from './contactsService';
export { default as LocationService } from './locationService';
export { default as CalendarService } from './calendarService';
export { default as AuthService } from './authService';
export { default as SharingService } from './sharingService';

// Export types
export type { AlumniContact } from './contactsService';
export type { LocationCoordinates, AlumniEvent as LocationAlumniEvent, LocationRegion } from './locationService';
export type { AlumniCalendarEvent, CalendarInfo } from './calendarService';
export type { AuthUser, AuthConfig } from './authService';
export type { ShareableContent, AlumniAchievement, AlumniEvent as SharingAlumniEvent } from './sharingService';
