import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../api/trpc';
import { useAlumniStore } from '../stores/alumniStore';

// Query keys
export const alumniKeys = {
  all: ['alumni'] as const,
  lists: () => [...alumniKeys.all, 'list'] as const,
  list: (filters: any) => [...alumniKeys.lists(), { filters }] as const,
  details: () => [...alumniKeys.all, 'detail'] as const,
  detail: (id: string) => [...alumniKeys.details(), id] as const,
};

// Get all alumni with filters
export function useAlumni(filters?: any) {
  const { setAlumni, setLoading } = useAlumniStore();

  return useQuery({
    queryKey: alumniKeys.list(filters),
    queryFn: async () => {
      setLoading(true);
      try {
        // TODO: Replace with actual tRPC call when API is connected
        // const result = await api.alumni.getAll.query(filters);
        
        // Mock data for now
        const mockAlumni = [
          {
            id: '1',
            email: '<EMAIL>',
            name: '<PERSON>',
            graduationYear: 2020,
            programmes: ['Computer Science'],
            currentRole: 'Software Engineer',
            company: 'Tech Corp',
            industry: 'Technology',
            skills: ['JavaScript', 'React', 'Node.js'],
            interests: ['AI', 'Web Development'],
            province: 'Gauteng',
            city: 'Johannesburg',
            country: 'South Africa',
            role: 'ALUMNI' as const,
            isActive: true,
          },
        ];
        
        setAlumni(mockAlumni);
        return mockAlumni;
      } finally {
        setLoading(false);
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get single alumni profile
export function useAlumniProfile(id: string) {
  return useQuery({
    queryKey: alumniKeys.detail(id),
    queryFn: async () => {
      // TODO: Replace with actual tRPC call
      // return api.alumni.getById.query({ id });
      
      // Mock data for now
      return {
        id,
        email: '<EMAIL>',
        name: 'John Doe',
        bio: 'Passionate software engineer with 5 years of experience.',
        graduationYear: 2020,
        programmes: ['Computer Science'],
        currentRole: 'Software Engineer',
        company: 'Tech Corp',
        industry: 'Technology',
        skills: ['JavaScript', 'React', 'Node.js'],
        interests: ['AI', 'Web Development'],
        province: 'Gauteng',
        city: 'Johannesburg',
        country: 'South Africa',
        role: 'ALUMNI' as const,
        isActive: true,
      };
    },
    enabled: !!id,
  });
}

// Update alumni profile
export function useUpdateAlumniProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      // TODO: Replace with actual tRPC call
      // return api.alumni.update.mutate(data);
      
      // Mock success for now
      return { success: true, data };
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch alumni queries
      queryClient.invalidateQueries({ queryKey: alumniKeys.all });
      
      // Update specific alumni in cache if we have the ID
      if (variables.id) {
        queryClient.setQueryData(
          alumniKeys.detail(variables.id),
          data
        );
      }
    },
  });
}

// Search alumni
export function useSearchAlumni(query: string) {
  return useQuery({
    queryKey: [...alumniKeys.lists(), 'search', query],
    queryFn: async () => {
      if (!query.trim()) return [];
      
      // TODO: Replace with actual tRPC call
      // return api.alumni.search.query({ query });
      
      // Mock search results
      return [
        {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          currentRole: 'Software Engineer',
          company: 'Tech Corp',
          graduationYear: 2020,
        },
      ];
    },
    enabled: query.length > 2,
    staleTime: 30 * 1000, // 30 seconds
  });
}
