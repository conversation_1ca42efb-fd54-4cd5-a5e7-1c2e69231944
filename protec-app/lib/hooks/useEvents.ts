import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../api/trpc';
import { useEventsStore } from '../stores/eventsStore';

// Query keys
export const eventsKeys = {
  all: ['events'] as const,
  lists: () => [...eventsKeys.all, 'list'] as const,
  list: (filters: any) => [...eventsKeys.lists(), { filters }] as const,
  details: () => [...eventsKeys.all, 'detail'] as const,
  detail: (id: string) => [...eventsKeys.details(), id] as const,
  myEvents: () => [...eventsKeys.all, 'myEvents'] as const,
};

// Get all events with filters
export function useEvents(filters?: any) {
  const { setEvents, setLoading } = useEventsStore();

  return useQuery({
    queryKey: eventsKeys.list(filters),
    queryFn: async () => {
      setLoading(true);
      try {
        // TODO: Replace with actual tRPC call
        // const result = await api.events.getAll.query(filters);
        
        // Mock data for now
        const mockEvents = [
          {
            id: '1',
            title: 'PROTEC Alumni Networking Event',
            description: 'Join us for an evening of networking and professional development.',
            category: 'Networking',
            startTime: new Date('2024-08-15T18:00:00'),
            endTime: new Date('2024-08-15T21:00:00'),
            location: {
              address: '123 Business District, Johannesburg',
              city: 'Johannesburg',
              province: 'Gauteng',
            },
            organizer: {
              id: '1',
              name: 'Jane Smith',
              email: '<EMAIL>',
            },
            organizerId: '1',
            isPublic: true,
            maxAttendees: 100,
            tags: ['networking', 'professional-development'],
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ];
        
        setEvents(mockEvents);
        return mockEvents;
      } finally {
        setLoading(false);
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get single event
export function useEvent(id: string) {
  return useQuery({
    queryKey: eventsKeys.detail(id),
    queryFn: async () => {
      // TODO: Replace with actual tRPC call
      // return api.events.getById.query({ id });
      
      // Mock data for now
      return {
        id,
        title: 'PROTEC Alumni Networking Event',
        description: 'Join us for an evening of networking and professional development. This event will feature guest speakers from various industries and provide opportunities to connect with fellow alumni.',
        category: 'Networking',
        startTime: new Date('2024-08-15T18:00:00'),
        endTime: new Date('2024-08-15T21:00:00'),
        location: {
          address: '123 Business District, Johannesburg',
          city: 'Johannesburg',
          province: 'Gauteng',
        },
        organizer: {
          id: '1',
          name: 'Jane Smith',
          email: '<EMAIL>',
          currentRole: 'Event Coordinator',
          graduationYear: 2018,
        },
        organizerId: '1',
        isPublic: true,
        maxAttendees: 100,
        tags: ['networking', 'professional-development'],
        imageUrl: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        rsvps: [],
      };
    },
    enabled: !!id,
  });
}

// Get user's events (RSVP'd events)
export function useMyEvents() {
  const { setMyEvents } = useEventsStore();

  return useQuery({
    queryKey: eventsKeys.myEvents(),
    queryFn: async () => {
      // TODO: Replace with actual tRPC call
      // return api.events.getMyEvents.query();
      
      // Mock data for now
      const myEvents = [];
      setMyEvents(myEvents);
      return myEvents;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// RSVP to event
export function useRSVPEvent() {
  const queryClient = useQueryClient();
  const { addToMyEvents } = useEventsStore();

  return useMutation({
    mutationFn: async ({ eventId, status }: { eventId: string; status: 'ATTENDING' | 'NOT_ATTENDING' }) => {
      // TODO: Replace with actual tRPC call
      // return api.events.rsvp.mutate({ eventId, status });
      
      // Mock success for now
      return { success: true, eventId, status };
    },
    onSuccess: (data, variables) => {
      // Invalidate events queries
      queryClient.invalidateQueries({ queryKey: eventsKeys.all });
      
      // If attending, add to my events
      if (variables.status === 'ATTENDING') {
        const event = queryClient.getQueryData(eventsKeys.detail(variables.eventId));
        if (event) {
          addToMyEvents(event as any);
        }
      }
    },
  });
}

// Create new event
export function useCreateEvent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (eventData: any) => {
      // TODO: Replace with actual tRPC call
      // return api.events.create.mutate(eventData);
      
      // Mock success for now
      return { success: true, id: 'new-event-id', ...eventData };
    },
    onSuccess: () => {
      // Invalidate events queries to refetch
      queryClient.invalidateQueries({ queryKey: eventsKeys.all });
    },
  });
}
