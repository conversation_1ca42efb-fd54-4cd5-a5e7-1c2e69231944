{"cli": {"version": ">= 12.0.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium"}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": true}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"ios": {"resourceClass": "m-medium"}, "android": {"buildType": "app-bundle"}, "env": {"NODE_ENV": "production"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal"}}}}